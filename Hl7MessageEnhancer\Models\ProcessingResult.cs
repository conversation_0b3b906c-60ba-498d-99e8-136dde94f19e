namespace Hl7MessageEnhancer.Models;

/// <summary>
/// Represents the result of processing an HL7 message
/// </summary>
public class ProcessingResult
{
    /// <summary>
    /// The enhanced HL7 message content
    /// </summary>
    public string EnhancedContent { get; set; } = string.Empty;

    /// <summary>
    /// Indicates if the processing was successful
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// Indicates if the message was enhanced with additional data
    /// </summary>
    public bool WasEnhanced { get; set; }

    /// <summary>
    /// The extracted MRN from the message
    /// </summary>
    public string? ExtractedMrn { get; set; }

    /// <summary>
    /// Additional fields extracted from the message
    /// </summary>
    public Dictionary<string, string> ExtractedFields { get; set; } = new();

    /// <summary>
    /// Error message if processing failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Patient data found in the database (if any)
    /// </summary>
    public Patient? PatientData { get; set; }
}

using Hl7MessageEnhancer.Services;

namespace TestMessageEnhancer;

public class MappingRulesProviderTests
{
    [Fact]
    public void GetDefaultMappingRules_ShouldReturnExpectedRules()
    {
        // Act
        var rules = MappingRulesProvider.GetDefaultMappingRules();

        // Assert
        rules.Should().NotBeNull();
        rules.Should().HaveCount(5);

        // Verify specific rules exist
        var qatarIdRule = rules.FirstOrDefault(r => r.ObxValue == "QATAR_ID_EXP");
        qatarIdRule.Should().NotBeNull();
        qatarIdRule!.RemoveOriginal.Should().BeTrue();
        qatarIdRule.TargetSegment.Should().BeEmpty();
        qatarIdRule.Description.Should().Contain("Remove Qatar ID expiration");

        var hcExpDateRule = rules.FirstOrDefault(r => r.ObxValue == "HC EXP DATE");
        hcExpDateRule.Should().NotBeNull();
        hcExpDateRule!.TargetSegment.Should().Be("PID");
        hcExpDateRule.TargetField.Should().Be("3.1.8");
        hcExpDateRule.RemoveOriginal.Should().BeTrue();

        var familyPhysicianRule = rules.FirstOrDefault(r => r.ObxValue == "FAMILY_PHYSICIAN");
        familyPhysicianRule.Should().NotBeNull();
        familyPhysicianRule!.TargetSegment.Should().Be("ROL");
        familyPhysicianRule.TargetField.Should().Be("4");

        var primOrgRule = rules.FirstOrDefault(r => r.ObxValue == "PRIM_ORG_NAME");
        primOrgRule.Should().NotBeNull();
        primOrgRule!.TargetSegment.Should().Be("PD1");
        primOrgRule.TargetField.Should().Be("3");

        var fullRegRule = rules.FirstOrDefault(r => r.ObxValue == "FULLREG");
        fullRegRule.Should().NotBeNull();
        fullRegRule!.TargetSegment.Should().Be("PID");
        fullRegRule.TargetField.Should().Be("30");
        fullRegRule.ValueMapping.Should().NotBeNull();
        fullRegRule.ValueMapping!["*"].Should().Be("Y");
    }

    [Fact]
    public void GetMappingRulesForConfiguration_WithDefault_ShouldReturnDefaultRules()
    {
        // Act
        var rules = MappingRulesProvider.GetMappingRulesForConfiguration("DEFAULT");

        // Assert
        rules.Should().NotBeNull();
        rules.Should().HaveCount(5);
        rules.Should().BeEquivalentTo(MappingRulesProvider.GetDefaultMappingRules());
    }

    [Fact]
    public void GetMappingRulesForConfiguration_WithPHCC_ShouldReturnDefaultRules()
    {
        // Act
        var rules = MappingRulesProvider.GetMappingRulesForConfiguration("PHCC");

        // Assert
        rules.Should().NotBeNull();
        rules.Should().HaveCount(5);
        rules.Should().BeEquivalentTo(MappingRulesProvider.GetDefaultMappingRules());
    }

    [Fact]
    public void GetMappingRulesForConfiguration_WithMinimal_ShouldReturnMinimalRules()
    {
        // Act
        var rules = MappingRulesProvider.GetMappingRulesForConfiguration("MINIMAL");

        // Assert
        rules.Should().NotBeNull();
        rules.Should().HaveCount(1);
        
        var fullRegRule = rules.First();
        fullRegRule.ObxValue.Should().Be("FULLREG");
        fullRegRule.TargetSegment.Should().Be("PID");
        fullRegRule.TargetField.Should().Be("30");
        fullRegRule.ValueMapping.Should().NotBeNull();
        fullRegRule.ValueMapping!["*"].Should().Be("Y");
    }

    [Fact]
    public void GetMappingRulesForConfiguration_WithUnknownConfiguration_ShouldReturnDefaultRules()
    {
        // Act
        var rules = MappingRulesProvider.GetMappingRulesForConfiguration("UNKNOWN_CONFIG");

        // Assert
        rules.Should().NotBeNull();
        rules.Should().HaveCount(5);
        rules.Should().BeEquivalentTo(MappingRulesProvider.GetDefaultMappingRules());
    }

    [Fact]
    public void GetMappingRulesForConfiguration_WithCaseInsensitive_ShouldWork()
    {
        // Act
        var rulesLower = MappingRulesProvider.GetMappingRulesForConfiguration("default");
        var rulesUpper = MappingRulesProvider.GetMappingRulesForConfiguration("DEFAULT");
        var rulesMixed = MappingRulesProvider.GetMappingRulesForConfiguration("Default");

        // Assert
        rulesLower.Should().BeEquivalentTo(rulesUpper);
        rulesUpper.Should().BeEquivalentTo(rulesMixed);
        rulesLower.Should().HaveCount(5);
    }

    [Fact]
    public void DefaultMappingRules_ShouldHaveValidProperties()
    {
        // Act
        var rules = MappingRulesProvider.GetDefaultMappingRules();

        // Assert
        foreach (var rule in rules)
        {
            rule.ObxField.Should().NotBeNullOrEmpty();
            rule.ObxValue.Should().NotBeNullOrEmpty();
            rule.Description.Should().NotBeNullOrEmpty();
            
            // If not a removal-only rule, should have target segment and field
            if (!string.IsNullOrEmpty(rule.TargetSegment))
            {
                rule.TargetField.Should().NotBeNullOrEmpty();
            }
        }
    }

    [Fact]
    public void DefaultMappingRules_ShouldHaveUniqueObxValues()
    {
        // Act
        var rules = MappingRulesProvider.GetDefaultMappingRules();

        // Assert
        var obxValues = rules.Select(r => r.ObxValue).ToList();
        obxValues.Should().OnlyHaveUniqueItems();
    }
}

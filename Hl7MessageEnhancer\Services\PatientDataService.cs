using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Interfaces;
using Hl7MessageEnhancer.Models;

namespace Hl7MessageEnhancer.Services;

/// <summary>
/// Service for retrieving patient data from the database
/// </summary>
public class PatientDataService : IPatientDataService
{
    private readonly HmcLiveFeedContext _context;
    private readonly ILogger<PatientDataService> _logger;

    public PatientDataService(HmcLiveFeedContext context, ILogger<PatientDataService> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Get patient data by MRN (Medical Record Number)
    /// </summary>
    /// <param name="mrn">Medical Record Number</param>
    /// <returns>Patient data if found, null otherwise</returns>
    public async Task<Patient?> GetPatientByMrnAsync(string mrn)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(mrn))
            {
                _logger.LogWarning("MRN is null or empty");
                return null;
            }

            _logger.LogDebug("Searching for patient with MRN: {Mrn}", mrn);

            // Search by HC_Number which typically contains the MRN
            var patient = await _context.Patients
                .FirstOrDefaultAsync(p => p.HcNumber == mrn);

            if (patient != null)
            {
                _logger.LogInformation("Found patient with MRN {Mrn}: QID={QId}", mrn, patient.QId);
            }
            else
            {
                _logger.LogWarning("No patient found with MRN: {Mrn}", mrn);
            }

            return patient;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving patient by MRN: {Mrn}", mrn);
            return null;
        }
    }

    /// <summary>
    /// Get patient data by Qatar ID
    /// </summary>
    /// <param name="qatarId">Qatar ID</param>
    /// <returns>Patient data if found, null otherwise</returns>
    public async Task<Patient?> GetPatientByQatarIdAsync(string qatarId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(qatarId))
            {
                _logger.LogWarning("Qatar ID is null or empty");
                return null;
            }

            _logger.LogDebug("Searching for patient with Qatar ID: {QatarId}", qatarId);

            var patient = await _context.Patients
                .FirstOrDefaultAsync(p => p.QId == qatarId);

            if (patient != null)
            {
                _logger.LogInformation("Found patient with Qatar ID {QatarId}", qatarId);
            }
            else
            {
                _logger.LogWarning("No patient found with Qatar ID: {QatarId}", qatarId);
            }

            return patient;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving patient by Qatar ID: {QatarId}", qatarId);
            return null;
        }
    }

    /// <summary>
    /// Get patient data by HC Number
    /// </summary>
    /// <param name="hcNumber">HC Number</param>
    /// <returns>Patient data if found, null otherwise</returns>
    public async Task<Patient?> GetPatientByHcNumberAsync(string hcNumber)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(hcNumber))
            {
                _logger.LogWarning("HC Number is null or empty");
                return null;
            }

            _logger.LogDebug("Searching for patient with HC Number: {HcNumber}", hcNumber);

            var patient = await _context.Patients
                .FirstOrDefaultAsync(p => p.HcNumber == hcNumber);

            if (patient != null)
            {
                _logger.LogInformation("Found patient with HC Number {HcNumber}: QID={QId}", hcNumber, patient.QId);
            }
            else
            {
                _logger.LogWarning("No patient found with HC Number: {HcNumber}", hcNumber);
            }

            return patient;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving patient by HC Number: {HcNumber}", hcNumber);
            return null;
        }
    }
}

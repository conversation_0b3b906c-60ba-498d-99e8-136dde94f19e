using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;

namespace TestMessageEnhancer;

public class IntegrationTests : IDisposable
{
    private readonly ILogger<Hl7Processor> _logger;
    private readonly string _testDataDirectory;
    private readonly string _sourceDirectory;
    private readonly string _outputDirectory;

    public IntegrationTests()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<Hl7Processor>();
        _testDataDirectory = Path.Combine(Directory.GetCurrentDirectory(), "IntegrationTestData");
        _sourceDirectory = Path.Combine(_testDataDirectory, "source");
        _outputDirectory = Path.Combine(_testDataDirectory, "output");
        
        SetupTestEnvironment();
    }

    private void SetupTestEnvironment()
    {
        // Clean up any existing test data
        if (Directory.Exists(_testDataDirectory))
        {
            Directory.Delete(_testDataDirectory, true);
        }

        // Create test directories
        Directory.CreateDirectory(_testDataDirectory);
        Directory.CreateDirectory(_sourceDirectory);

        // Note: Mapping rules are now hardcoded in the application, no need to create JSON file

        // Create test HL7 file
        var testHl7Content = "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.3||||||8859/1\r" +
                            "EVN|A04|20231019232741|||56269^Bhuvaneswari^Gopika^Raj^^^^^External Id^Personnel^^^External Identifier^\\R\\56269\r" +
                            "PID|1||HC02287391^^^MRN^MR^RHAPSODY_CON_SYS||ABED^AHMED^^^^^official||19930804000000|male||Non National|||||||||||||||Yemeni||No\r" +
                            "PV1|1|Outpatient|ABS Nursing|||||||||||||||||||||||||||||||||||||||||||ABS Abu Baker||Active|||20231019232741\r" +
                            "OBX|1|DT|FULLREG||20231019||||||\r" +
                            "OBX|2|DT|HC EXP DATE||20240405||||||";

        File.WriteAllText(Path.Combine(_sourceDirectory, "test-sample.hl7"), testHl7Content);
    }

    [Fact]
    public void Hl7Processor_ProcessSingleFile_ShouldSucceed()
    {
        // Arrange
        var currentDir = Directory.GetCurrentDirectory();
        Directory.SetCurrentDirectory(_testDataDirectory);

        try
        {
            var processor = new Hl7Processor(_logger, "source", "output", true);

            // Act
            processor.Run();

            // Assert
            processor.Statistics.FilesProcessed.Should().Be(1);
            processor.Statistics.FilesEnhanced.Should().Be(1);
            processor.Statistics.ErrorsEncountered.Should().Be(0);
            processor.Statistics.FilesQuarantined.Should().Be(0);

            // Verify output file exists
            var outputFile = Path.Combine(_outputDirectory, "test-sample.hl7");
            File.Exists(outputFile).Should().BeTrue();

            // Verify output content
            var outputContent = File.ReadAllText(outputFile);
            outputContent.Should().NotBeNullOrEmpty();
            outputContent.Should().Contain("MSH");
            outputContent.Should().Contain("EVN");
            outputContent.Should().Contain("PID");
        }
        finally
        {
            Directory.SetCurrentDirectory(currentDir);
        }
    }

    [Fact]
    public void Hl7Processor_ProcessEmptyDirectory_ShouldHandleGracefully()
    {
        // Arrange
        var emptySourceDir = Path.Combine(_testDataDirectory, "empty");
        Directory.CreateDirectory(emptySourceDir);
        
        var currentDir = Directory.GetCurrentDirectory();
        Directory.SetCurrentDirectory(_testDataDirectory);

        try
        {
            var processor = new Hl7Processor(_logger, "empty", "output", false);

            // Act
            processor.Run();

            // Assert
            processor.Statistics.FilesProcessed.Should().Be(0);
            processor.Statistics.FilesEnhanced.Should().Be(0);
            processor.Statistics.ErrorsEncountered.Should().Be(0);
            processor.Statistics.FilesQuarantined.Should().Be(0);
        }
        finally
        {
            Directory.SetCurrentDirectory(currentDir);
        }
    }

    [Fact]
    public void Hl7Processor_ProcessInvalidHL7File_ShouldQuarantineFile()
    {
        // Arrange
        var invalidHl7Content = "INVALID|HL7|CONTENT|WITHOUT|PROPER|STRUCTURE";
        var invalidFile = Path.Combine(_sourceDirectory, "invalid.hl7");
        File.WriteAllText(invalidFile, invalidHl7Content);

        var currentDir = Directory.GetCurrentDirectory();
        Directory.SetCurrentDirectory(_testDataDirectory);

        try
        {
            var processor = new Hl7Processor(_logger, "source", "output", false);

            // Act
            processor.Run();

            // Assert
            processor.Statistics.FilesProcessed.Should().Be(2); // original test file + invalid file
            processor.Statistics.FilesEnhanced.Should().Be(1); // only the valid file
            processor.Statistics.ErrorsEncountered.Should().Be(1);
            processor.Statistics.FilesQuarantined.Should().Be(1);

            // Verify quarantine directory exists and contains the invalid file
            var quarantineDir = Path.Combine(_testDataDirectory, "quarantine");
            Directory.Exists(quarantineDir).Should().BeTrue();
            
            var quarantinedFile = Path.Combine(quarantineDir, "invalid.hl7");
            File.Exists(quarantinedFile).Should().BeTrue();

            // Verify error details file exists
            var errorFile = Path.Combine(quarantineDir, "invalid.error.json");
            File.Exists(errorFile).Should().BeTrue();
        }
        finally
        {
            Directory.SetCurrentDirectory(currentDir);
        }
    }

    [Fact]
    public void Hl7Processor_ProcessFileWithHardcodedMappingRules_ShouldProcess()
    {
        // Arrange
        // Note: Mapping rules are now hardcoded, so this test verifies they work correctly
        var currentDir = Directory.GetCurrentDirectory();
        Directory.SetCurrentDirectory(_testDataDirectory);

        try
        {
            var processor = new Hl7Processor(_logger, "source", "output", false);

            // Act
            processor.Run();

            // Assert
            processor.Statistics.FilesProcessed.Should().Be(1);
            processor.Statistics.FilesEnhanced.Should().Be(1);
            processor.Statistics.ErrorsEncountered.Should().Be(0);
            processor.Statistics.FilesQuarantined.Should().Be(0);

            // Verify output file exists
            var outputFile = Path.Combine(_outputDirectory, "test-sample.hl7");
            File.Exists(outputFile).Should().BeTrue();
        }
        finally
        {
            Directory.SetCurrentDirectory(currentDir);
        }
    }

    [Fact]
    public void Hl7Processor_ProcessMultipleFiles_ShouldProcessAll()
    {
        // Arrange
        // Create additional test files
        var testHl7Content2 = "MSH|^~\\&|System2|PHCC|RHAPSODY_ADT|PHCC|20231020120000||ADT^A08|MSG002|P|2.3||||||8859/1\r" +
                             "EVN|A08|20231020120000|||12345^Doctor^Test^^^^^External Id^Personnel^^^External Identifier^\r" +
                             "PID|1||TEST123^^^MRN^MR||TEST^PATIENT^^^^^official||19900101000000|female||Non National|||||||||||||||American||No\r" +
                             "PV1|1|Outpatient|Test Unit|||||||||||||||||||||||||||||||||||||||||||Test Hospital||Active|||20231020120000\r" +
                             "OBX|1|DT|FULLREG||20231020||||||";

        File.WriteAllText(Path.Combine(_sourceDirectory, "test-sample2.hl7"), testHl7Content2);

        var currentDir = Directory.GetCurrentDirectory();
        Directory.SetCurrentDirectory(_testDataDirectory);

        try
        {
            var processor = new Hl7Processor(_logger, "source", "output", false);

            // Act
            processor.Run();

            // Assert
            processor.Statistics.FilesProcessed.Should().Be(2);
            processor.Statistics.FilesEnhanced.Should().Be(2);
            processor.Statistics.ErrorsEncountered.Should().Be(0);
            processor.Statistics.FilesQuarantined.Should().Be(0);

            // Verify both output files exist
            File.Exists(Path.Combine(_outputDirectory, "test-sample.hl7")).Should().BeTrue();
            File.Exists(Path.Combine(_outputDirectory, "test-sample2.hl7")).Should().BeTrue();
        }
        finally
        {
            Directory.SetCurrentDirectory(currentDir);
        }
    }

    public void Dispose()
    {
        if (Directory.Exists(_testDataDirectory))
        {
            try
            {
                Directory.Delete(_testDataDirectory, true);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Warning: Could not clean up test directory {_testDataDirectory}: {ex.Message}");
                // Don't throw - cleanup failures shouldn't fail tests
            }
        }
    }
}

using Newtonsoft.Json;

namespace Hl7MessageEnhancer.Models;

/// <summary>
/// Represents a mapping rule for transforming OBX segments to other HL7 segments
/// </summary>
public class MappingRule
{
    /// <summary>
    /// The OBX field to match (e.g., "OBX-3.1")
    /// </summary>
    [JsonProperty("obxField")]
    public string ObxField { get; set; } = string.Empty;

    /// <summary>
    /// The value in the OBX field to match
    /// </summary>
    [JsonProperty("obxValue")]
    public string ObxValue { get; set; } = string.Empty;

    /// <summary>
    /// The target segment to map to (e.g., "PID", "ROL", "PD1")
    /// </summary>
    [JsonProperty("targetSegment")]
    public string TargetSegment { get; set; } = string.Empty;

    /// <summary>
    /// The target field in the segment (e.g., "3.x.8", "4", "30")
    /// </summary>
    [JsonProperty("targetField")]
    public string TargetField { get; set; } = string.Empty;

    /// <summary>
    /// Whether to remove the original OBX segment after mapping
    /// </summary>
    [JsonProperty("removeOriginal")]
    public bool RemoveOriginal { get; set; }

    /// <summary>
    /// Description of what this mapping rule does
    /// </summary>
    [JsonProperty("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Optional value mapping for transforming values
    /// </summary>
    [JsonProperty("valueMapping")]
    public Dictionary<string, string>? ValueMapping { get; set; }
}

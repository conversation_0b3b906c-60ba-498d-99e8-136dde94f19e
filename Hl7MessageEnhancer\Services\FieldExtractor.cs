using Microsoft.Extensions.Logging;
using NHapi.Base.Model;
using System.Text.RegularExpressions;

namespace Hl7MessageEnhancer.Services;

/// <summary>
/// Service for extracting specific fields from HL7 messages
/// </summary>
public class FieldExtractor
{
    private readonly ILogger<FieldExtractor> _logger;

    public FieldExtractor(ILogger<FieldExtractor> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Extract MRN from PID.3 segment of the HL7 message
    /// </summary>
    /// <param name="message">Parsed HL7 message</param>
    /// <returns>MRN if found, null otherwise</returns>
    public string? ExtractMrnFromMessage(IMessage message)
    {
        try
        {
            var pidSegment = (ISegment?)message.GetStructure("PID");
            if (pidSegment == null)
            {
                _logger.LogWarning("PID segment not found in message");
                return null;
            }

            // PID.3 contains patient identifiers
            var patientIdField = pidSegment.GetField(3, 0);
            if (patientIdField == null)
            {
                _logger.LogWarning("PID.3 field not found");
                return null;
            }

            var patientIdString = patientIdField.ToString();
            _logger.LogDebug("PID.3 content: {PatientId}", patientIdString);

            // Extract MRN from patient identifier list
            // Format is typically: ID^^^AssigningAuthority^IdentifierType
            // We look for MRN or MR identifier types
            var identifiers = patientIdString.Split('~');
            
            foreach (var identifier in identifiers)
            {
                var parts = identifier.Split('^');
                if (parts.Length >= 5)
                {
                    var idValue = parts[0];
                    var idType = parts[4];
                    
                    // Look for MRN or MR identifier types
                    if (idType.Equals("MRN", StringComparison.OrdinalIgnoreCase) || 
                        idType.Equals("MR", StringComparison.OrdinalIgnoreCase))
                    {
                        if (!string.IsNullOrWhiteSpace(idValue))
                        {
                            _logger.LogInformation("Extracted MRN: {Mrn}", idValue);
                            return idValue;
                        }
                    }
                }
            }

            // If no specific MRN type found, try to extract the first non-empty identifier
            foreach (var identifier in identifiers)
            {
                var parts = identifier.Split('^');
                if (parts.Length > 0 && !string.IsNullOrWhiteSpace(parts[0]))
                {
                    _logger.LogInformation("Extracted first available identifier as MRN: {Mrn}", parts[0]);
                    return parts[0];
                }
            }

            _logger.LogWarning("No MRN found in PID.3 field");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting MRN from message");
            return null;
        }
    }

    /// <summary>
    /// Extract specific fields from OBX segments and other parts of the message
    /// </summary>
    /// <param name="message">Parsed HL7 message</param>
    /// <returns>Dictionary of extracted fields</returns>
    public Dictionary<string, string> ExtractFieldsFromMessage(IMessage message)
    {
        var extractedFields = new Dictionary<string, string>();

        try
        {
            // Extract Qatar ID from PID segment
            ExtractQatarId(message, extractedFields);

            // Extract fields from OBX segments
            ExtractObxFields(message, extractedFields);

            // Extract additional PID fields
            ExtractPidFields(message, extractedFields);

            _logger.LogInformation("Extracted {Count} fields from message", extractedFields.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting fields from message");
        }

        return extractedFields;
    }

    /// <summary>
    /// Extract Qatar ID from PID segment
    /// </summary>
    private void ExtractQatarId(IMessage message, Dictionary<string, string> extractedFields)
    {
        try
        {
            var pidSegment = (ISegment?)message.GetStructure("PID");
            if (pidSegment == null) return;

            var patientIdField = pidSegment.GetField(3, 0);
            if (patientIdField == null) return;

            var patientIdString = patientIdField.ToString();
            var identifiers = patientIdString.Split('~');

            foreach (var identifier in identifiers)
            {
                var parts = identifier.Split('^');
                if (parts.Length >= 5)
                {
                    var idValue = parts[0];
                    var assigningAuthority = parts[3];
                    var idType = parts[4];

                    // Look for Qatar ID patterns
                    if (assigningAuthority.Contains("MOI", StringComparison.OrdinalIgnoreCase) ||
                        idType.Contains("SSN", StringComparison.OrdinalIgnoreCase) ||
                        IsQatarIdFormat(idValue))
                    {
                        extractedFields["QatarId"] = idValue;
                        _logger.LogDebug("Extracted Qatar ID: {QatarId}", idValue);
                        break;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting Qatar ID");
        }
    }

    /// <summary>
    /// Extract relevant fields from OBX segments
    /// </summary>
    private void ExtractObxFields(IMessage message, Dictionary<string, string> extractedFields)
    {
        try
        {
            // Convert message to string to parse OBX segments
            var messageString = message.ToString();
            var lines = messageString.Split(['\r', '\n'], StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines)
            {
                if (line.StartsWith("OBX|"))
                {
                    var fields = line.Split('|');
                    if (fields.Length >= 6)
                    {
                        var observationId = fields[3]; // OBX.3 - Observation Identifier
                        var observationValue = fields[5]; // OBX.5 - Observation Value

                        // Extract specific observation types
                        if (observationId.Contains("FULLREG", StringComparison.OrdinalIgnoreCase))
                        {
                            extractedFields["FullRegistrationDate"] = observationValue;
                            _logger.LogDebug("Extracted Full Registration Date: {Date}", observationValue);
                        }
                        else if (observationId.Contains("HC EXP DATE", StringComparison.OrdinalIgnoreCase))
                        {
                            extractedFields["HcExpiryDate"] = observationValue;
                            _logger.LogDebug("Extracted HC Expiry Date: {Date}", observationValue);
                        }
                        else if (observationId.Contains("QID EXP", StringComparison.OrdinalIgnoreCase))
                        {
                            extractedFields["QidExpiryDate"] = observationValue;
                            _logger.LogDebug("Extracted QID Expiry Date: {Date}", observationValue);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting OBX fields");
        }
    }

    /// <summary>
    /// Extract additional fields from PID segment
    /// </summary>
    private void ExtractPidFields(IMessage message, Dictionary<string, string> extractedFields)
    {
        try
        {
            var pidSegment = (ISegment?)message.GetStructure("PID");
            if (pidSegment == null) return;

            // Extract patient name (PID.5)
            var nameField = pidSegment.GetField(5, 0);
            if (nameField != null && !string.IsNullOrWhiteSpace(nameField.ToString()))
            {
                extractedFields["PatientName"] = nameField.ToString();
            }

            // Extract date of birth (PID.7)
            var dobField = pidSegment.GetField(7, 0);
            if (dobField != null && !string.IsNullOrWhiteSpace(dobField.ToString()))
            {
                extractedFields["DateOfBirth"] = dobField.ToString();
            }

            // Extract gender (PID.8)
            var genderField = pidSegment.GetField(8, 0);
            if (genderField != null && !string.IsNullOrWhiteSpace(genderField.ToString()))
            {
                extractedFields["Gender"] = genderField.ToString();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting PID fields");
        }
    }

    /// <summary>
    /// Check if a string matches Qatar ID format (11 digits)
    /// </summary>
    private bool IsQatarIdFormat(string value)
    {
        return !string.IsNullOrWhiteSpace(value) && 
               Regex.IsMatch(value, @"^\d{11}$");
    }
}

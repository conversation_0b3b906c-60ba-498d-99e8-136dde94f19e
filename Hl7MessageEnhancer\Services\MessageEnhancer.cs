using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Interfaces;
using Hl7MessageEnhancer.Models;
using System.Text.RegularExpressions;

namespace Hl7MessageEnhancer.Services;

/// <summary>
/// Service for enhancing HL7 messages with patient data from the database
/// </summary>
public class MessageEnhancer : IMessageEnhancer
{
    private readonly ILogger<MessageEnhancer> _logger;

    public MessageEnhancer(ILogger<MessageEnhancer> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Add missing fields to an HL7 message using patient data
    /// </summary>
    /// <param name="originalMessage">Original HL7 message content</param>
    /// <param name="patientData">Patient data from database</param>
    /// <returns>Enhanced message content and indication if changes were made</returns>
    public (string enhancedMessage, bool wasEnhanced) AddMissingFields(string originalMessage, Patient patientData)
    {
        try
        {
            _logger.LogDebug("Starting message enhancement with patient data for QID: {QId}", patientData.QId);

            var lines = originalMessage.Split(['\r', '\n'], StringSplitOptions.RemoveEmptyEntries).ToList();
            bool wasEnhanced = false;

            // Enhance PID segment
            wasEnhanced |= EnhancePidSegment(lines, patientData);

            // Add missing segments if needed
            wasEnhanced |= AddMissingSegments(lines, patientData);

            var enhancedMessage = string.Join("\r", lines);

            _logger.LogInformation("Message enhancement completed. Enhanced: {WasEnhanced}", wasEnhanced);

            return (enhancedMessage, wasEnhanced);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enhancing message with patient data");
            return (originalMessage, false);
        }
    }

    /// <summary>
    /// Enhance the PID segment with patient data
    /// </summary>
    private bool EnhancePidSegment(List<string> lines, Patient patientData)
    {
        bool wasEnhanced = false;

        for (int i = 0; i < lines.Count; i++)
        {
            if (lines[i].StartsWith("PID|"))
            {
                var pidFields = lines[i].Split('|');
                var originalPid = lines[i];

                // Ensure we have enough fields in the PID segment
                if (pidFields.Length < 29)
                {
                    var originalLength = pidFields.Length;
                    Array.Resize(ref pidFields, 29);
                    for (int j = originalLength; j < pidFields.Length; j++)
                    {
                        pidFields[j] = "";
                    }
                }

                // Enhance PID-5 (Patient Name) if missing or incomplete
                _logger.LogDebug("PID-5 current value: '{Value}'", pidFields[5]);
                if (string.IsNullOrWhiteSpace(pidFields[5]) || pidFields[5].Contains("^^^^^"))
                {
                    var patientName = BuildPatientName(patientData);
                    _logger.LogDebug("Built patient name: '{PatientName}'", patientName);
                    if (!string.IsNullOrWhiteSpace(patientName))
                    {
                        pidFields[5] = patientName;
                        wasEnhanced = true;
                        _logger.LogDebug("Enhanced PID-5 (Patient Name): {PatientName}", patientName);
                    }
                }

                // Enhance PID-7 (Date of Birth) if missing
                if (string.IsNullOrWhiteSpace(pidFields[7]) && patientData.DateOfBirth.HasValue)
                {
                    pidFields[7] = patientData.DateOfBirth.Value.ToString("yyyyMMdd");
                    wasEnhanced = true;
                    _logger.LogDebug("Enhanced PID-7 (Date of Birth): {DateOfBirth}", pidFields[7]);
                }

                // Enhance PID-8 (Administrative Sex) if missing
                if (string.IsNullOrWhiteSpace(pidFields[8]) && !string.IsNullOrWhiteSpace(patientData.GenderCode))
                {
                    pidFields[8] = MapGenderCode(patientData.GenderCode);
                    wasEnhanced = true;
                    _logger.LogDebug("Enhanced PID-8 (Administrative Sex): {Gender}", pidFields[8]);
                }

                // Enhance PID-13 (Phone Number) if missing
                if (string.IsNullOrWhiteSpace(pidFields[13]) && !string.IsNullOrWhiteSpace(patientData.MobileNumber))
                {
                    pidFields[13] = patientData.MobileNumber;
                    wasEnhanced = true;
                    _logger.LogDebug("Enhanced PID-13 (Phone Number): {PhoneNumber}", patientData.MobileNumber);
                }

                // Enhance PID-16 (Marital Status) if missing
                if (string.IsNullOrWhiteSpace(pidFields[16]) && !string.IsNullOrWhiteSpace(patientData.MaritalStatusCode))
                {
                    pidFields[16] = patientData.MaritalStatusCode;
                    wasEnhanced = true;
                    _logger.LogDebug("Enhanced PID-16 (Marital Status): {MaritalStatus}", patientData.MaritalStatusCode);
                }

                // Enhance PID-28 (Nationality) if missing
                if (string.IsNullOrWhiteSpace(pidFields[28]) && !string.IsNullOrWhiteSpace(patientData.NationalityCode))
                {
                    pidFields[28] = patientData.NationalityCode;
                    wasEnhanced = true;
                    _logger.LogDebug("Enhanced PID-28 (Nationality): {Nationality}", patientData.NationalityCode);
                }

                // Rebuild the PID segment
                lines[i] = string.Join("|", pidFields);

                break; // Only process the first PID segment
            }
        }

        return wasEnhanced;
    }

    /// <summary>
    /// Add missing segments based on patient data
    /// </summary>
    private bool AddMissingSegments(List<string> lines, Patient patientData)
    {
        bool wasEnhanced = false;

        // Add PD1 segment if missing and we have assigned physician or HC code
        if (!lines.Any(l => l.StartsWith("PD1|")) && 
            (!string.IsNullOrWhiteSpace(patientData.AssignedFamilyPhysicianId) || 
             !string.IsNullOrWhiteSpace(patientData.AssignedHcCode)))
        {
            var pd1Segment = BuildPd1Segment(patientData);
            if (!string.IsNullOrWhiteSpace(pd1Segment))
            {
                // Insert PD1 after PID
                var pidIndex = lines.FindIndex(l => l.StartsWith("PID|"));
                if (pidIndex >= 0)
                {
                    lines.Insert(pidIndex + 1, pd1Segment);
                    wasEnhanced = true;
                    _logger.LogDebug("Added PD1 segment: {PD1Segment}", pd1Segment);
                }
            }
        }

        return wasEnhanced;
    }

    /// <summary>
    /// Build patient name from database fields
    /// </summary>
    private string BuildPatientName(Patient patientData)
    {
        var nameParts = new List<string>();

        if (!string.IsNullOrWhiteSpace(patientData.LastNameEn))
            nameParts.Add(patientData.LastNameEn);

        if (!string.IsNullOrWhiteSpace(patientData.FirstNameEn))
            nameParts.Add(patientData.FirstNameEn);

        if (!string.IsNullOrWhiteSpace(patientData.MiddleNameEn))
            nameParts.Add(patientData.MiddleNameEn);

        // Pad to ensure we have at least 5 components (Last^First^Middle^^^)
        while (nameParts.Count < 5)
            nameParts.Add("");

        return string.Join("^", nameParts);
    }

    /// <summary>
    /// Map gender code to HL7 format
    /// </summary>
    private string MapGenderCode(string genderCode)
    {
        return genderCode.ToUpper() switch
        {
            "M" or "MALE" => "M",
            "F" or "FEMALE" => "F",
            "U" or "UNKNOWN" => "U",
            _ => genderCode
        };
    }

    /// <summary>
    /// Build PD1 segment from patient data
    /// </summary>
    private string BuildPd1Segment(Patient patientData)
    {
        var pd1Fields = new string[9]; // PD1 has 8 fields after the segment ID
        pd1Fields[0] = "PD1"; // Segment ID

        // PD1-3: Patient Primary Facility
        if (!string.IsNullOrWhiteSpace(patientData.AssignedHcCode))
        {
            pd1Fields[3] = patientData.AssignedHcCode;
        }

        // PD1-4: Patient Primary Care Provider
        if (!string.IsNullOrWhiteSpace(patientData.AssignedFamilyPhysicianId))
        {
            pd1Fields[4] = patientData.AssignedFamilyPhysicianId;
        }

        // Only create the segment if we have meaningful data
        if (!string.IsNullOrWhiteSpace(pd1Fields[3]) || !string.IsNullOrWhiteSpace(pd1Fields[4]))
        {
            return string.Join("|", pd1Fields);
        }

        return string.Empty;
    }
}

using Microsoft.Extensions.Logging;
using NHapi.Base.Parser;
using Hl7MessageEnhancer.Interfaces;
using Hl7MessageEnhancer.Models;

namespace Hl7MessageEnhancer.Services;

/// <summary>
/// Enhanced HL7 processor that includes database lookup functionality
/// </summary>
public class EnhancedHl7Processor
{
    private readonly ILogger<EnhancedHl7Processor> _logger;
    private readonly Hl7MessageProcessor _baseProcessor;
    private readonly IPatientDataService _patientDataService;
    private readonly IMessageEnhancer _messageEnhancer;
    private readonly FieldExtractor _fieldExtractor;
    private readonly PipeParser _parser;
    private readonly bool _enableDatabaseLookup;

    public EnhancedHl7Processor(
        ILogger<EnhancedHl7Processor> logger,
        Hl7MessageProcessor baseProcessor,
        IPatientDataService patientDataService,
        IMessageEnhancer messageEnhancer,
        ILoggerFactory loggerFactory,
        bool enableDatabaseLookup = true)
    {
        _logger = logger;
        _baseProcessor = baseProcessor;
        _patientDataService = patientDataService;
        _messageEnhancer = messageEnhancer;
        _fieldExtractor = new FieldExtractor(loggerFactory.CreateLogger<FieldExtractor>());
        _parser = new PipeParser();
        _enableDatabaseLookup = enableDatabaseLookup;
    }

    /// <summary>
    /// Process HL7 message with database enhancement
    /// </summary>
    /// <param name="hl7Content">Raw HL7 message content</param>
    /// <returns>Processing result with enhanced content</returns>
    public async Task<ProcessingResult> ProcessHl7MessageAsync(string hl7Content)
    {
        var result = new ProcessingResult
        {
            IsSuccessful = false,
            WasEnhanced = false
        };

        try
        {
            _logger.LogInformation("Starting enhanced HL7 message processing");

            // Step 1: Apply standard HL7 enhancements (remapping fields)
            string enhancedContent;
            try
            {
                enhancedContent = _baseProcessor.ProcessHl7Message(hl7Content);
                result.IsSuccessful = true;
                _logger.LogDebug("Standard HL7 enhancements applied successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to apply standard HL7 enhancements");
                result.ErrorMessage = $"Standard enhancement failed: {ex.Message}";
                return result;
            }

            // Step 2: Parse the enhanced message for field extraction
            var parsedMessage = _parser.Parse(enhancedContent);

            // Step 3: Extract MRN from the message
            var extractedMrn = _fieldExtractor.ExtractMrnFromMessage(parsedMessage);
            result.ExtractedMrn = extractedMrn;

            if (string.IsNullOrWhiteSpace(extractedMrn))
            {
                _logger.LogWarning("MRN not found or empty in message");
                result.ErrorMessage = "MRN not found in message";
                result.EnhancedContent = enhancedContent;
                return result;
            }

            _logger.LogInformation("Extracted MRN: {Mrn}", extractedMrn);

            // Step 4: Extract other fields from the message
            result.ExtractedFields = _fieldExtractor.ExtractFieldsFromMessage(parsedMessage);

            // Step 5: Database lookup and message enhancement (if enabled)
            if (_enableDatabaseLookup)
            {
                _logger.LogInformation("Database lookup enabled, searching for patient data");

                var patientData = await _patientDataService.GetPatientByMrnAsync(extractedMrn);
                result.PatientData = patientData;

                if (patientData != null)
                {
                    _logger.LogInformation("Patient record found for MRN {Mrn}: QID={QId}", 
                        extractedMrn, patientData.QId);

                    // Enhance the message with database data
                    var (finalEnhancedContent, wasEnhanced) = _messageEnhancer.AddMissingFields(enhancedContent, patientData);
                    
                    result.EnhancedContent = finalEnhancedContent;
                    result.WasEnhanced = wasEnhanced;

                    if (wasEnhanced)
                    {
                        _logger.LogInformation("Message enhanced with database data");
                    }
                    else
                    {
                        _logger.LogInformation("No additional enhancements needed from database data");
                    }
                }
                else
                {
                    _logger.LogWarning("No patient record found for MRN: {Mrn}", extractedMrn);
                    result.EnhancedContent = enhancedContent;
                }
            }
            else
            {
                _logger.LogInformation("Database lookup disabled, proceeding with standard enhancements only");
                result.EnhancedContent = enhancedContent;
            }

            _logger.LogInformation("Enhanced HL7 message processing completed successfully");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during enhanced HL7 message processing");
            result.ErrorMessage = ex.Message;
            result.EnhancedContent = hl7Content; // Return original content on error
            return result;
        }
    }

    /// <summary>
    /// Process HL7 message and return only the enhanced content (for backward compatibility)
    /// </summary>
    /// <param name="hl7Content">Raw HL7 message content</param>
    /// <returns>Enhanced HL7 message content</returns>
    public async Task<string> ProcessHl7MessageSimpleAsync(string hl7Content)
    {
        var result = await ProcessHl7MessageAsync(hl7Content);
        return result.EnhancedContent;
    }

    /// <summary>
    /// Get processing statistics from the base processor
    /// </summary>
    public ProcessingStatistics Statistics => _baseProcessor.Statistics;
}

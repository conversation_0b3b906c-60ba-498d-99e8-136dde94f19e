using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Hl7MessageEnhancer.Models;

[Table("AppointmentBookings")]
public class AppointmentBooking
{
    [Column("HC_Number")]
    [StringLength(20)]
    public string? HcNumber { get; set; }

    [Column("QID")] 
    [StringLength(20)] 
    public string? QId { get; set; }

    [Key] 
    [Column("Appointment_Id")] 
    public long AppointmentId { get; set; }

    [Column("Initial_Appointment_Date_time")]
    public DateTime? InitialAppointmentDateTime { get; set; }

    [Column("Appointment_Date_time")] 
    public DateTime? AppointmentDateTime { get; set; }

    [Column("Appointment_Type_code")]
    [StringLength(300)]
    public string? AppointmentTypeCode { get; set; }

    [Column("Appointment_Status")]
    [StringLength(100)]
    public string? AppointmentStatus { get; set; }

    [Column("Clinic_Type_code")]
    [StringLength(300)]
    public string? ClinicTypeCode { get; set; }

    [Column("Appointment_HC_Code")]
    [StringLength(100)]
    public string? AppointmentHcCode { get; set; }

    [Column("Appointment_Physician_Id")]
    [StringLength(300)]
    public string? AppointmentPhysicianId { get; set; }

    [Column("Appointment_duration")] 
    public int? AppointmentDuration { get; set; }

    [Column("Appointment_Consultation_Type")]
    [StringLength(100)]
    public string? AppointmentConsultationType { get; set; }

    [Column("Insert_Date")] 
    public DateTime? InsertDate { get; set; }

    [Column("Update_Date")] 
    public DateTime? UpdateDate { get; set; }

    [Column("Insert_message_id")]
    [StringLength(150)]
    public string? InsertMessageId { get; set; }

    [Column("Update_message_id")]
    [StringLength(150)]
    public string? UpdateMessageId { get; set; }

    [Column("EVN_Date")]
    public DateTime? EvnDate { get; set; }

    // Navigation property
    public virtual Patient Patient { get; set; } = null!;
}

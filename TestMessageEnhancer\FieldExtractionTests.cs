using Microsoft.Extensions.Logging;
using NHapi.Base.Parser;
using Xunit;
using Hl7MessageEnhancer.Services;

namespace TestMessageEnhancer;

/// <summary>
/// Tests for field extraction functionality
/// </summary>
public class FieldExtractionTests
{
    private readonly FieldExtractor _fieldExtractor;
    private readonly PipeParser _parser;

    public FieldExtractionTests()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<FieldExtractor>();
        _fieldExtractor = new FieldExtractor(logger);
        _parser = new PipeParser();
    }

    [Fact]
    public void ExtractMrnFromMessage_WithValidMrn_ShouldReturnMrn()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||HC123456^^^HMC^MRN||Doe^John^Michael||19900515|M";

        var parsedMessage = _parser.Parse(hl7Message);

        // Act
        var result = _fieldExtractor.ExtractMrnFromMessage(parsedMessage);

        // Assert
        Assert.Equal("HC123456", result);
    }

    [Fact]
    public void ExtractMrnFromMessage_WithMultipleIdentifiers_ShouldReturnMrn()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||12345678901^^^MOI^SSN~HC123456^^^HMC^MRN||Doe^John^Michael||19900515|M";

        var parsedMessage = _parser.Parse(hl7Message);

        // Act
        var result = _fieldExtractor.ExtractMrnFromMessage(parsedMessage);

        // Assert
        Assert.Equal("HC123456", result);
    }

    [Fact]
    public void ExtractMrnFromMessage_WithNoMrn_ShouldReturnFirstIdentifier()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||12345678901^^^MOI^SSN||Doe^John^Michael||19900515|M";

        var parsedMessage = _parser.Parse(hl7Message);

        // Act
        var result = _fieldExtractor.ExtractMrnFromMessage(parsedMessage);

        // Assert
        Assert.Equal("12345678901", result);
    }

    [Fact]
    public void ExtractFieldsFromMessage_WithCompleteMessage_ShouldExtractAllFields()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||12345678901^^^MOI^SSN~HC123456^^^HMC^MRN||Doe^John^Michael||19900515|M|||||+97450123456|||S||||||||QAT\r" +
                        "OBX|1|DT|FULLREG||20240101||||||F\r" +
                        "OBX|2|DT|HC EXP DATE||20261231||||||F\r" +
                        "OBX|3|DT|QID EXP||20291231||||||F";

        var parsedMessage = _parser.Parse(hl7Message);

        // Act
        var result = _fieldExtractor.ExtractFieldsFromMessage(parsedMessage);

        // Assert
        Assert.Contains("QatarId", result.Keys);
        Assert.Equal("12345678901", result["QatarId"]);
        
        Assert.Contains("PatientName", result.Keys);
        Assert.Equal("Doe^John^Michael", result["PatientName"]);
        
        Assert.Contains("DateOfBirth", result.Keys);
        Assert.Equal("19900515", result["DateOfBirth"]);
        
        Assert.Contains("Gender", result.Keys);
        Assert.Equal("M", result["Gender"]);
        
        Assert.Contains("FullRegistrationDate", result.Keys);
        Assert.Equal("20240101", result["FullRegistrationDate"]);
        
        Assert.Contains("HcExpiryDate", result.Keys);
        Assert.Equal("20261231", result["HcExpiryDate"]);
        
        Assert.Contains("QidExpiryDate", result.Keys);
        Assert.Equal("20291231", result["QidExpiryDate"]);
    }

    [Fact]
    public void ExtractFieldsFromMessage_WithMinimalMessage_ShouldExtractAvailableFields()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||HC123456^^^HMC^MRN||||||||||||||||||||||||||";

        var parsedMessage = _parser.Parse(hl7Message);

        // Act
        var result = _fieldExtractor.ExtractFieldsFromMessage(parsedMessage);

        // Assert
        // Should not contain Qatar ID since it's not in MOI format
        Assert.DoesNotContain("QatarId", result.Keys);
        
        // Should not contain other fields since they're empty
        Assert.DoesNotContain("PatientName", result.Keys);
        Assert.DoesNotContain("DateOfBirth", result.Keys);
        Assert.DoesNotContain("Gender", result.Keys);
    }

    [Fact]
    public void ExtractFieldsFromMessage_WithQatarIdFormat_ShouldExtractQatarId()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||12345678901^^^SYSTEM^ID||||||||||||||||||||||||||";

        var parsedMessage = _parser.Parse(hl7Message);

        // Act
        var result = _fieldExtractor.ExtractFieldsFromMessage(parsedMessage);

        // Assert
        Assert.Contains("QatarId", result.Keys);
        Assert.Equal("12345678901", result["QatarId"]);
    }
}

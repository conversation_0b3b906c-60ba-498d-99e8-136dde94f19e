﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Hl7MessageEnhancer.Services;
using Hl7MessageEnhancer.Exceptions;
using Hl7MessageEnhancer.Models;
using Hl7MessageEnhancer.Interfaces;

namespace Hl7MessageEnhancer;

/// <summary>
/// Main program entry point for HL7 Message Enhancement Engine
/// </summary>
public class Program
{
    /// <summary>
    /// Main entry point
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Exit code</returns>
    public static async Task<int> Main(string[] args)
    {
        try
        {
            var options = ParseArguments(args);

            // Validate source directory
            if (!Directory.Exists(options.SourceDirectory))
            {
                Console.WriteLine($"ERROR: Source directory not found: {options.SourceDirectory}");
                return 1;
            }

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .Build();

            // Setup dependency injection
            var services = new ServiceCollection();
            ConfigureServices(services, configuration, options);

            using var serviceProvider = services.BuildServiceProvider();

            // Check if enhanced processing is enabled
            var dbConfig = configuration.GetSection("Database").Get<DatabaseConfiguration>() ?? new DatabaseConfiguration();

            if (dbConfig.EnableDatabaseLookup && !string.IsNullOrWhiteSpace(dbConfig.ConnectionString))
            {
                Console.WriteLine("Enhanced HL7 processing with database lookup enabled");
                await RunEnhancedProcessorAsync(serviceProvider, options);
            }
            else
            {
                Console.WriteLine("Standard HL7 processing (database lookup disabled)");
                RunStandardProcessor(serviceProvider, options);
            }

            return 0;
        }
        catch (Hl7ProcessingException ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            return 1;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: Failed to initialize processor: {ex.Message}");
            return 1;
        }
    }

    /// <summary>
    /// Parse command line arguments
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Parsed options</returns>
    private static CommandLineOptions ParseArguments(string[] args)
    {
        var options = new CommandLineOptions();

        for (var i = 0; i < args.Length; i++)
        {
            switch (args[i])
            {
                case "--source":
                    if (i + 1 < args.Length)
                    {
                        options.SourceDirectory = args[++i];
                    }
                    break;
                case "--output":
                    if (i + 1 < args.Length)
                    {
                        options.OutputDirectory = args[++i];
                    }
                    break;
                case "--verbose":
                case "-v":
                    options.Verbose = true;
                    break;
                case "--config":
                case "-c":
                    if (i + 1 < args.Length)
                    {
                        options.Configuration = args[++i];
                    }
                    break;
                case "--help":
                case "-h":
                    ShowHelp();
                    Environment.Exit(0);
                    break;
            }
        }

        return options;
    }

    /// <summary>
    /// Configure dependency injection services
    /// </summary>
    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration, CommandLineOptions options)
    {
        // Add logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(options.Verbose ? LogLevel.Debug : LogLevel.Information);
        });

        // Add configuration
        services.AddSingleton(configuration);

        // Add database configuration
        var dbConfig = configuration.GetSection("Database").Get<DatabaseConfiguration>() ?? new DatabaseConfiguration();
        services.AddSingleton(dbConfig);

        // Add database context if enabled
        if (dbConfig.EnableDatabaseLookup && !string.IsNullOrWhiteSpace(dbConfig.ConnectionString))
        {
            services.AddDbContext<HmcLiveFeedContext>(options =>
                options.UseSqlServer(dbConfig.ConnectionString, sqlOptions =>
                    sqlOptions.CommandTimeout(dbConfig.TimeoutSeconds)));

            // Add database services
            services.AddScoped<IPatientDataService, PatientDataService>();
            services.AddScoped<IMessageEnhancer, MessageEnhancer>();
        }

        // Add core services
        services.AddScoped<Hl7MessageProcessor>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<Hl7MessageProcessor>>();
            return new Hl7MessageProcessor(logger, options.Configuration);
        });

        services.AddScoped<EnhancedHl7Processor>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<EnhancedHl7Processor>>();
            var baseProcessor = provider.GetRequiredService<Hl7MessageProcessor>();
            var patientDataService = provider.GetService<IPatientDataService>();
            var messageEnhancer = provider.GetService<IMessageEnhancer>();
            var loggerFactory = provider.GetRequiredService<ILoggerFactory>();

            return new EnhancedHl7Processor(
                logger,
                baseProcessor,
                patientDataService!,
                messageEnhancer!,
                loggerFactory,
                dbConfig.EnableDatabaseLookup);
        });
    }

    /// <summary>
    /// Run the enhanced processor with database lookup
    /// </summary>
    private static async Task RunEnhancedProcessorAsync(IServiceProvider serviceProvider, CommandLineOptions options)
    {
        var enhancedProcessor = serviceProvider.GetRequiredService<EnhancedHl7Processor>();

        // Process all files in the source directory
        var sourceFiles = Directory.GetFiles(options.SourceDirectory, "*.hl7", SearchOption.AllDirectories);

        foreach (var sourceFile in sourceFiles)
        {
            try
            {
                var content = await File.ReadAllTextAsync(sourceFile);
                var result = await enhancedProcessor.ProcessHl7MessageAsync(content);

                if (result.IsSuccessful)
                {
                    var relativePath = Path.GetRelativePath(options.SourceDirectory, sourceFile);
                    var outputPath = Path.Combine(options.OutputDirectory, relativePath);
                    var outputDir = Path.GetDirectoryName(outputPath);

                    if (!string.IsNullOrEmpty(outputDir))
                    {
                        Directory.CreateDirectory(outputDir);
                    }

                    await File.WriteAllTextAsync(outputPath, result.EnhancedContent);
                    Console.WriteLine($"Processed: {sourceFile} -> {outputPath} (Enhanced: {result.WasEnhanced})");
                }
                else
                {
                    Console.WriteLine($"Failed to process: {sourceFile} - {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing {sourceFile}: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Run the standard processor without database lookup
    /// </summary>
    private static void RunStandardProcessor(IServiceProvider serviceProvider, CommandLineOptions options)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Hl7Processor>>();
        var processor = new Hl7Processor(logger, options.SourceDirectory, options.OutputDirectory, options.Verbose, options.Configuration);
        processor.Run();
    }

    /// <summary>
    /// Show help information
    /// </summary>
    private static void ShowHelp()
    {
        Console.WriteLine("HL7 Message Enhancement Engine");
        Console.WriteLine();
        Console.WriteLine("Usage: Hl7MessageEnhancer [options]");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  --source <directory>    Source directory for raw HL7 files (default: rawhl7messages)");
        Console.WriteLine("  --output <directory>    Output directory for enhanced files (default: enhancedHl7)");
        Console.WriteLine("  --config <name>, -c        Mapping rules configuration (default: DEFAULT, options: DEFAULT, PHCC, MINIMAL)");
        Console.WriteLine("  --verbose, -v           Enable verbose logging");
        Console.WriteLine("  --help, -h              Show this help message");
        Console.WriteLine();
        Console.WriteLine("Database Enhancement:");
        Console.WriteLine("  Configure database settings in appsettings.json to enable enhanced processing");
        Console.WriteLine("  with patient data lookup and field enhancement.");
    }
}

/// <summary>
/// Command line options
/// </summary>
public class CommandLineOptions
{
    /// <summary>
    /// Source directory for raw HL7 files
    /// </summary>
    public string SourceDirectory { get; set; } = "rawhl7messages";

    /// <summary>
    /// Output directory for enhanced files
    /// </summary>
    public string OutputDirectory { get; set; } = "enhancedHl7";

    /// <summary>
    /// Enable verbose logging
    /// </summary>
    public bool Verbose { get; set; }

    /// <summary>
    /// Mapping rules configuration name
    /// </summary>
    public string Configuration { get; set; } = "DEFAULT";
}
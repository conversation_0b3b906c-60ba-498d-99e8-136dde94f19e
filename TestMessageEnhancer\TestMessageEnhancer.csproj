<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="FluentAssertions" Version="8.5.0" />
        <PackageReference Include="NHapi.Base" Version="3.2.3" />
        <PackageReference Include="NHapi.Model.V28" Version="3.2.4" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.7" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
        <Using Include="FluentAssertions"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Hl7MessageEnhancer\Hl7MessageEnhancer.csproj" />
    </ItemGroup>

</Project>

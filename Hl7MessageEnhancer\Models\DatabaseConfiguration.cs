namespace Hl7MessageEnhancer.Models;

/// <summary>
/// Configuration for database connection and settings
/// </summary>
public class DatabaseConfiguration
{
    /// <summary>
    /// Database connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Enable or disable database lookup functionality
    /// </summary>
    public bool EnableDatabaseLookup { get; set; } = false;

    /// <summary>
    /// Timeout for database operations in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;
}

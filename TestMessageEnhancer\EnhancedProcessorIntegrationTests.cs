using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Xunit;
using Hl7MessageEnhancer.Models;
using Hl7MessageEnhancer.Services;
using Hl7MessageEnhancer.Interfaces;

namespace TestMessageEnhancer;

/// <summary>
/// Integration tests for the enhanced HL7 processor with database functionality
/// </summary>
public class EnhancedProcessorIntegrationTests : IDisposable
{
    private readonly HmcLiveFeedContext _context;
    private readonly EnhancedHl7Processor _enhancedProcessor;
    private readonly ILoggerFactory _loggerFactory;

    public EnhancedProcessorIntegrationTests()
    {
        // Setup in-memory database for testing
        var options = new DbContextOptionsBuilder<HmcLiveFeedContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new HmcLiveFeedContext(options);

        // Setup loggers
        _loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());

        // Setup services
        var patientDataService = new PatientDataService(_context, _loggerFactory.CreateLogger<PatientDataService>());
        var messageEnhancer = new MessageEnhancer(_loggerFactory.CreateLogger<MessageEnhancer>());
        var baseProcessor = new Hl7MessageProcessor(_loggerFactory.CreateLogger<Hl7MessageProcessor>());

        _enhancedProcessor = new EnhancedHl7Processor(
            _loggerFactory.CreateLogger<EnhancedHl7Processor>(),
            baseProcessor,
            patientDataService,
            messageEnhancer,
            _loggerFactory,
            enableDatabaseLookup: true);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        var testPatient = new Patient
        {
            QId = "12345678901",
            QidExpiryDate = DateTime.Now.AddYears(5),
            FirstNameEn = "John",
            MiddleNameEn = "Michael",
            LastNameEn = "Doe",
            FirstNameAr = "جون",
            MiddleNameAr = "مايكل",
            LastNameAr = "دو",
            DateOfBirth = new DateTime(1990, 5, 15),
            NationalityCode = "QAT",
            GenderCode = "M",
            MobileNumber = "+97450123456",
            HcNumber = "HC123456",
            HcExpiryDate = DateTime.Now.AddYears(2),
            GisAddressStreet = "Al Corniche Street",
            GisAddressBuilding = "Building 123",
            GisAddressZone = "Zone 45",
            GisAddressUnit = "Unit 567",
            AssignedFamilyPhysicianId = "DOC001",
            AssignedHcCode = "HMC001",
            MaritalStatusCode = "S",
            InsertDate = DateTime.Now,
            UpdateDate = DateTime.Now
        };

        _context.Patients.Add(testPatient);
        _context.SaveChanges();
    }

    [Fact]
    public async Task ProcessHl7MessageAsync_WithKnownPatient_ShouldEnhanceMessage()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||HC123456^^^HMC^MRN||^^^^^||||||||||||||||||||||||||";

        // Act
        var result = await _enhancedProcessor.ProcessHl7MessageAsync(hl7Message);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.True(result.WasEnhanced);
        Assert.Equal("HC123456", result.ExtractedMrn);
        Assert.NotNull(result.PatientData);
        Assert.Equal("12345678901", result.PatientData.QId);
        
        // Verify the message was enhanced with patient data
        Assert.Contains("Doe^John^Michael", result.EnhancedContent);
        Assert.Contains("19900515", result.EnhancedContent);
        Assert.Contains("M", result.EnhancedContent);
    }

    [Fact]
    public async Task ProcessHl7MessageAsync_WithUnknownPatient_ShouldNotEnhanceMessage()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||UNKNOWN123^^^HMC^MRN||^^^^^||||||||||||||||||||||||||";

        // Act
        var result = await _enhancedProcessor.ProcessHl7MessageAsync(hl7Message);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.False(result.WasEnhanced);
        Assert.Equal("UNKNOWN123", result.ExtractedMrn);
        Assert.Null(result.PatientData);
        
        // Should still contain the standard enhanced content but not database enhancements
        Assert.NotNull(result.EnhancedContent);
    }

    [Fact]
    public async Task ProcessHl7MessageAsync_WithNoMrn_ShouldFailGracefully()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||||^^^^^||||||||||||||||||||||||||";

        // Act
        var result = await _enhancedProcessor.ProcessHl7MessageAsync(hl7Message);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.False(result.WasEnhanced);
        Assert.Null(result.ExtractedMrn);
        Assert.Null(result.PatientData);
        Assert.Contains("MRN not found", result.ErrorMessage);
    }

    [Fact]
    public async Task ProcessHl7MessageAsync_WithCompleteMessage_ShouldApplyStandardEnhancementsOnly()
    {
        // Arrange - Message already has complete patient data
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||HC123456^^^HMC^MRN||Doe^John^Michael||19900515|M|||||+97450123456|||S||||||||QAT";

        // Act
        var result = await _enhancedProcessor.ProcessHl7MessageAsync(hl7Message);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.Equal("HC123456", result.ExtractedMrn);
        Assert.NotNull(result.PatientData);
        
        // Message should not be enhanced since it already has complete data
        // (The WasEnhanced flag indicates if database data was used to enhance the message)
        Assert.NotNull(result.EnhancedContent);
    }

    [Fact]
    public async Task ProcessHl7MessageAsync_ShouldExtractFieldsCorrectly()
    {
        // Arrange
        var hl7Message = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                        "PID|1||12345678901^^^MOI^SSN~HC123456^^^HMC^MRN||Doe^John^Michael||19900515|M|||||+97450123456|||S||||||||QAT\r" +
                        "OBX|1|DT|FULLREG||20240101||||||F\r" +
                        "OBX|2|DT|HC EXP DATE||20261231||||||F";

        // Act
        var result = await _enhancedProcessor.ProcessHl7MessageAsync(hl7Message);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.Equal("HC123456", result.ExtractedMrn);
        
        // Check extracted fields
        Assert.Contains("QatarId", result.ExtractedFields.Keys);
        Assert.Equal("12345678901", result.ExtractedFields["QatarId"]);
        
        Assert.Contains("PatientName", result.ExtractedFields.Keys);
        Assert.Equal("Doe^John^Michael", result.ExtractedFields["PatientName"]);
        
        Assert.Contains("FullRegistrationDate", result.ExtractedFields.Keys);
        Assert.Equal("20240101", result.ExtractedFields["FullRegistrationDate"]);
        
        Assert.Contains("HcExpiryDate", result.ExtractedFields.Keys);
        Assert.Equal("20261231", result.ExtractedFields["HcExpiryDate"]);
    }

    public void Dispose()
    {
        _context.Dispose();
        _loggerFactory.Dispose();
    }
}

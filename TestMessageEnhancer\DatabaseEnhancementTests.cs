using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Xunit;
using Hl7MessageEnhancer.Models;
using Hl7MessageEnhancer.Services;
using Hl7MessageEnhancer.Interfaces;

namespace TestMessageEnhancer;

/// <summary>
/// Tests for database enhancement functionality
/// </summary>
public class DatabaseEnhancementTests : IDisposable
{
    private readonly HmcLiveFeedContext _context;
    private readonly IPatientDataService _patientDataService;
    private readonly IMessageEnhancer _messageEnhancer;
    private readonly ILogger<PatientDataService> _patientLogger;
    private readonly ILogger<MessageEnhancer> _enhancerLogger;

    public DatabaseEnhancementTests()
    {
        // Setup in-memory database for testing
        var options = new DbContextOptionsBuilder<HmcLiveFeedContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new HmcLiveFeedContext(options);

        // Setup loggers
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _patientLogger = loggerFactory.CreateLogger<PatientDataService>();
        _enhancerLogger = loggerFactory.CreateLogger<MessageEnhancer>();

        // Setup services
        _patientDataService = new PatientDataService(_context, _patientLogger);
        _messageEnhancer = new MessageEnhancer(_enhancerLogger);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        var testPatient = new Patient
        {
            QId = "12345678901",
            QidExpiryDate = DateTime.Now.AddYears(5),
            FirstNameEn = "John",
            MiddleNameEn = "Michael",
            LastNameEn = "Doe",
            FirstNameAr = "جون",
            MiddleNameAr = "مايكل",
            LastNameAr = "دو",
            DateOfBirth = new DateTime(1990, 5, 15),
            NationalityCode = "QAT",
            GenderCode = "M",
            MobileNumber = "+97450123456",
            HcNumber = "HC123456",
            HcExpiryDate = DateTime.Now.AddYears(2),
            GisAddressStreet = "Al Corniche Street",
            GisAddressBuilding = "Building 123",
            GisAddressZone = "Zone 45",
            GisAddressUnit = "Unit 567",
            AssignedFamilyPhysicianId = "DOC001",
            AssignedHcCode = "HMC001",
            MaritalStatusCode = "S",
            InsertDate = DateTime.Now,
            UpdateDate = DateTime.Now
        };

        _context.Patients.Add(testPatient);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GetPatientByMrnAsync_WithValidMrn_ShouldReturnPatient()
    {
        // Arrange
        var mrn = "HC123456";

        // Act
        var result = await _patientDataService.GetPatientByMrnAsync(mrn);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("12345678901", result.QId);
        Assert.Equal("John", result.FirstNameEn);
        Assert.Equal("Doe", result.LastNameEn);
    }

    [Fact]
    public async Task GetPatientByMrnAsync_WithInvalidMrn_ShouldReturnNull()
    {
        // Arrange
        var mrn = "INVALID_MRN";

        // Act
        var result = await _patientDataService.GetPatientByMrnAsync(mrn);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetPatientByQatarIdAsync_WithValidQatarId_ShouldReturnPatient()
    {
        // Arrange
        var qatarId = "12345678901";

        // Act
        var result = await _patientDataService.GetPatientByQatarIdAsync(qatarId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(qatarId, result.QId);
        Assert.Equal("HC123456", result.HcNumber);
    }

    [Fact]
    public void AddMissingFields_WithPatientData_ShouldEnhanceMessage()
    {
        // Arrange
        var originalMessage = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                             "PID|1||HC123456^^^HMC^MRN||^^^^^||||||||||||||||||||||||||";

        var patientData = new Patient
        {
            QId = "12345678901",
            FirstNameEn = "John",
            LastNameEn = "Doe",
            DateOfBirth = new DateTime(1990, 5, 15),
            GenderCode = "M",
            MobileNumber = "+97450123456",
            MaritalStatusCode = "S",
            NationalityCode = "QAT"
        };

        // Act
        var (enhancedMessage, wasEnhanced) = _messageEnhancer.AddMissingFields(originalMessage, patientData);

        // Assert
        Console.WriteLine($"Enhanced message: {enhancedMessage}");
        Assert.True(wasEnhanced);
        Assert.Contains("Doe^John", enhancedMessage);
        Assert.Contains("19900515", enhancedMessage);
        Assert.Contains("M", enhancedMessage);
    }

    [Fact]
    public void AddMissingFields_WithCompleteMessage_ShouldNotEnhance()
    {
        // Arrange
        var originalMessage = "MSH|^~\\&|SYSTEM|FACILITY|RECEIVER|DESTINATION|20240101120000||ADT^A08|MSG001|P|2.8\r" +
                             "PID|1||HC123456^^^HMC^MRN||Doe^John^Michael||19900515|M|||||+97450123456|||S||||||||QAT";

        var patientData = new Patient
        {
            QId = "12345678901",
            FirstNameEn = "John",
            LastNameEn = "Doe",
            DateOfBirth = new DateTime(1990, 5, 15),
            GenderCode = "M"
        };

        // Act
        var (enhancedMessage, wasEnhanced) = _messageEnhancer.AddMissingFields(originalMessage, patientData);

        // Assert
        Assert.False(wasEnhanced);
        Assert.Equal(originalMessage, enhancedMessage);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}

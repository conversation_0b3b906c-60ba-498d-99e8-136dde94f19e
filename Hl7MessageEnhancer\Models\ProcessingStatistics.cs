namespace Hl7MessageEnhancer.Models;

/// <summary>
/// Statistics for HL7 processing operations
/// </summary>
public class ProcessingStatistics
{
    /// <summary>
    /// Total number of files processed
    /// </summary>
    public int FilesProcessed { get; set; }

    /// <summary>
    /// Number of files successfully enhanced
    /// </summary>
    public int FilesEnhanced { get; set; }

    /// <summary>
    /// Number of errors encountered during processing
    /// </summary>
    public int ErrorsEncountered { get; set; }

    /// <summary>
    /// Number of files moved to quarantine
    /// </summary>
    public int FilesQuarantined { get; set; }

    /// <summary>
    /// Reset all statistics to zero
    /// </summary>
    public void Reset()
    {
        FilesProcessed = 0;
        FilesEnhanced = 0;
        ErrorsEncountered = 0;
        FilesQuarantined = 0;
    }
}

using Hl7MessageEnhancer.Models;

namespace Hl7MessageEnhancer.Interfaces;

/// <summary>
/// Service for retrieving patient data from the database
/// </summary>
public interface IPatientDataService
{
    /// <summary>
    /// Get patient data by MRN (Medical Record Number)
    /// </summary>
    /// <param name="mrn">Medical Record Number</param>
    /// <returns>Patient data if found, null otherwise</returns>
    Task<Patient?> GetPatientByMrnAsync(string mrn);

    /// <summary>
    /// Get patient data by Qatar ID
    /// </summary>
    /// <param name="qatarId">Qatar ID</param>
    /// <returns>Patient data if found, null otherwise</returns>
    Task<Patient?> GetPatientByQatarIdAsync(string qatarId);

    /// <summary>
    /// Get patient data by HC Number
    /// </summary>
    /// <param name="hcNumber">HC Number</param>
    /// <returns>Patient data if found, null otherwise</returns>
    Task<Patient?> GetPatientByHcNumberAsync(string hcNumber);
}

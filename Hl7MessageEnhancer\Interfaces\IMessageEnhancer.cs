using Hl7MessageEnhancer.Models;

namespace Hl7MessageEnhancer.Interfaces;

/// <summary>
/// Service for enhancing HL7 messages with patient data
/// </summary>
public interface IMessageEnhancer
{
    /// <summary>
    /// Add missing fields to an HL7 message using patient data
    /// </summary>
    /// <param name="originalMessage">Original HL7 message content</param>
    /// <param name="patientData">Patient data from database</param>
    /// <returns>Enhanced message content and indication if changes were made</returns>
    (string enhancedMessage, bool wasEnhanced) AddMissingFields(string originalMessage, Patient patientData);
}

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Hl7MessageEnhancer.Models;

[Table("Visits")]
public class Visit
{
    [Column("HC_Number")]
    [StringLength(20)]
    public string? HcNumber { get; set; }

    [Column("QID")] 
    [StringLength(20)] 
    public string? QId { get; set; }

    [Key] 
    [Column("Visit_Number")] 
    public long VisitNumber { get; set; }

    [Column("FIN_Number")] 
    public long? FinNumber { get; set; }

    [Column("Visit_Date_time")] 
    public DateTime? VisitDateTime { get; set; }

    [Column("Visit_HC")]
    [StringLength(100)]
    public string? VisitHc { get; set; }

    [Column("Visit_Type")]
    [StringLength(300)]
    public string? VisitType { get; set; }

    [Column("Visit_Clinic")]
    [StringLength(300)]
    public string? VisitClinic { get; set; }

    [Column("Attending_Physician")]
    [StringLength(300)]
    public string? AttendingPhysician { get; set; }

    [Column("Healthcare_Service")]
    [StringLength(100)]
    public string? HealthcareService { get; set; }

    [Column("Admit_Source")]
    [StringLength(100)]
    public string? AdmitSource { get; set; }

    [Column("Insert_Date")] 
    public DateTime? InsertDate { get; set; }

    [Column("Update_Date")] 
    public DateTime? UpdateDate { get; set; }

    [Column("Insert_message_id")]
    [StringLength(150)]
    public string? InsertMessageId { get; set; }

    [Column("Update_message_id")]
    [StringLength(150)]
    public string? UpdateMessageId { get; set; }
        
    // Navigation property
    public virtual Patient Patient { get; set; } = null!;
}

# HL7 Message Enhancer

A comprehensive HL7 message processing and enhancement system that transforms HL7 v2.x messages according to standardized enhancement rules while maintaining clinical data integrity.

## Overview

This project processes HL7 messages by applying systematic transformations to improve message structure, eliminate redundancy, and ensure compliance with HL7 v2.8 standards. The enhancement process is conditional based on message type and follows a structured approach to data migration and cleanup.

## Architecture

The system consists of two main processing services:

- **HL7Processor**: File-based processing service that handles file I/O operations
- **HL7MessageProcessor**: Core message processing service focused on message transformation logic

### Key Components

- `Services/HL7Processor.cs` - File-based HL7 processing with directory management
- `Services/HL7MessageProcessor.cs` - Core message transformation logic
- `Services/MappingRulesProvider.cs` - Hardcoded mapping rules for OBX transformations
- `Models/MappingRule.cs` - Data model for transformation rules
- `Exceptions/HL7ProcessingException.cs` - Custom exception handling

## How HL7 Messages Are Processed

### 1. Pre-Processing Rules

#### Conditional Enrichment Check
The system first inspects MSH-9 (Message Type) to determine if enhancement is appropriate:

**Enhanced Message Types:**
- ADT^A04 (Register a patient)
- ADT^A08 (Update patient information)
- ADT^A28 (Add person information)
- ADT^A31 (Update person information)
- SIU^S12 (New appointment)
- SIU^S13 (Reschedule appointment)

**Non-Enhanced Message Types:**
- ADT^A34 (Merge patient information)
- SIU^S15 (Cancel appointment)
- SIU^S26 (Cancel appointment notification)

If the message type is inappropriate for enrichment, the original message is returned without changes.

### 2. Global Changes

#### HL7 Version Upgrade
- **Rule**: MSH-12 (Version ID) is updated to 2.8 if the current version is older
- **Example**: `MSH|^~\&|...|P|2.3||||||8859/1` → `MSH|^~\&|...|P|2.8||||||8859/1`

#### OBX Segment Processing and Removal
The system processes OBX (Observation/Result) segments to extract structured data:
1. Scrutinizes all OBX segments using identifier in OBX-3.1
2. Migrates relevant data to appropriate segments
3. Deletes all original OBX segments after successful data migration

This significantly reduces message size while preserving essential information.

### 3. OBX Data Migration Rules

#### Expiration Date Migration
- **QATAR_ID_EXP** or **HC EXP DATE**: Values moved from OBX-5.1 to corresponding PID-3.8 (Expiration Date)

#### Primary Facility Migration
- **PRIM_ORG_NAME**: Value moved from OBX-5.1 to PD1-3 (Patient Primary Facility)

#### Family Physician ROL Segment Creation
- **FAMILY_PHYSICIAN**: Creates new ROL segment with:
  - ROL-1 (Role Instance ID): 1
  - ROL-2 (Action Code): AD
  - ROL-3 (Role): PP^Primary Care Provider^HL70443
  - ROL-4 (Role Person): Value from OBX-5.1
  - **Placement**: Inserted immediately after PID segment

### 4. PID Segment Transformations

#### Patient ID List Restructuring
- Consolidates multiple identifiers with expiration dates
- Standardizes identifier formats
- Reorders identifiers for consistency

#### Name Type Standardization
- `Current` → `official`
- `Alternate Character Set Current Name` → `usual`
- `Alternate` → `usual`

#### Gender Standardization
- Administrative Sex field (PID-8) expanded to full text and normalized:
  - `M` → `male`
  - `F` → `female`

#### Identifier Formatting Rules
- **Assigning Authority**: `medicom` values followed by two empty components (`medicom^^`)
- **MOI/SSN Quoting**: MOI and SSN values enclosed in double quotes (`^^^"MOI"^"SSN"`)

#### Phone Number Formatting
- Standardized with Qatar country code (+974)
- Type often changed to `mobile`
- Example: `55309888^Home^Tel` → `+97455309888^mobile^`

#### Patient Deceased Indicator Correction
- PID-30 (Patient Death Indicator): `No` or `N` → `Y`

### 5. Coded Value Expansion

#### Marital Status Expansion (PID-16)
- `S` → `Single`
- `M` → `Married`
- `D` → `Divorced`

#### Admit Reason Expansion (PV2-20)
- `Y` → `Yes`
- `N` → `No`
- `U` → `Unknown`

### 6. New Segment Additions

#### ROL Segment Addition
- Added after PID segment for family physician data
- Contains primary care provider information

#### PD1 Segment Enhancement
- Enhanced or added with facility information from OBX data
- Formatted with at least 8 fields and trailing pipes
- Example: `PD1|||Rawdat Al Khail Health Center|||||`

### 7. Segment Reordering

Segments are reordered to follow HL7 standard sequence:
1. MSH (Message Header)
2. EVN (Event Type)
3. PID (Patient Identification)
4. ROL (Role) - newly added after PID
5. PD1 (Patient Additional Demographic)
6. PV1 (Patient Visit)
7. PV2 (Patient Visit - Additional Info)
8. AL1 (Patient Allergy Information)
9. GT1 (Guarantor)
10. Other segments (NK1, AIL, SCH, ZZZ)

### 8. Cleanup and Finalization

#### Segment Cleanup
- Deletes all processed OBX segments
- Removes OBX segments with processing flags or temporary data
- Removes empty OBX segments

#### Segment Formatting
- Ensures segments contain required number of trailing empty fields (pipes)
- Maintains consistent formatting across similar data elements

## Processing Flow

```
1. Input HL7 Message
   ↓
2. Parse Message (NHapi Parser)
   ↓
3. Check Message Type (MSH-9)
   ↓
4. Apply Field Cleanup
   ↓
5. Ensure Required Segments
   ↓
6. Apply OBX Mapping Rules
   ↓
7. Apply String-Based Transformations
   ↓
8. Output Enhanced HL7 Message
```

## Usage

### File-Based Processing
```csharp
var processor = new Hl7Processor(logger, sourceDirectory, outputDirectory, verbose);
var files = processor.ProcessAllFiles();
```

### String-Based Processing
```csharp
var messageProcessor = new Hl7MessageProcessor(logger);
var enhancedMessage = messageProcessor.ProcessHl7Message(hl7Content);
```

### Object-Based Processing
```csharp
var messageProcessor = new Hl7MessageProcessor(logger);
var enhancedMessageObject = messageProcessor.ProcessHl7MessageToObject(hl7Content);
```

## Configuration

The system uses hardcoded mapping rules provided by `MappingRulesProvider`. Different configurations can be specified:
- `DEFAULT`: Standard enhancement rules
- Custom configurations can be added as needed

## Quality Improvements

### Data Consistency
- Ensures consistent formatting across similar data elements
- Standardizes phone number, date, and identifier formats

### Redundancy Elimination
- Removes duplicate or redundant information
- Primary target: OBX segments containing administrative data already present in other segments

## Benefits

- **Message Size Reduction**: Significantly reduces message size (e.g., from 52 lines to 9 lines)
- **Data Integrity**: Maintains clinical data integrity throughout transformation
- **Standards Compliance**: Ensures compliance with HL7 v2.8 standards
- **Structured Data**: Improves message structure and eliminates redundancy
- **Conditional Processing**: Only processes appropriate message types

## Error Handling

The system includes comprehensive error handling:
- Custom `Hl7ProcessingException` for specific error scenarios
- Detailed error logging with context information
- Quarantine directory for problematic files
- Processing statistics tracking

## Dependencies

- **NHapi**: HL7 message parsing and manipulation
- **Microsoft.Extensions.Logging**: Logging framework
- **Newtonsoft.Json**: JSON serialization for error logging

## File Structure

```
Hl7MessageEnhancer/
├── Services/
│   ├── HL7Processor.cs           # File-based processing
│   ├── HL7MessageProcessor.cs    # Core message processing
│   └── MappingRulesProvider.cs   # Mapping rules configuration
├── Models/
│   ├── MappingRule.cs           # Mapping rule data model
│   └── ProcessingStatistics.cs  # Processing statistics
├── Interfaces/
│   └── IHL7Processor.cs         # Processing interface
├── Exceptions/
│   └── HL7ProcessingException.cs # Custom exceptions
├── rawhl7messages/              # Input directory
├── enhancedHl7/                # Output directory
└── quarantine/                 # Error files
```

This comprehensive enhancement system ensures that HL7 messages are processed efficiently while maintaining data integrity and following industry standards.